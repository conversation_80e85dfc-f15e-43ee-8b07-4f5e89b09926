import { DateTime } from 'luxon';
import { inject, singleton } from 'tsyringe';

import { PlatformDataFetchedStatus, PlatformKey, TimeInSeconds } from '@malou-io/package-utils';

import { InjectionToken } from ':helpers/injection';
import { logger } from ':helpers/logger';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { send as sendFetchPlatformReviewsMessage } from ':modules/reviews/queues/update-reviews/update-reviews.producer';
import { Cache } from ':plugins/cache';

@singleton()
export class RefreshStalePlatformReviewsUseCase {
    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private _platformsRepository: PlatformsRepository,
        @inject(InjectionToken.Cache) private readonly _cache: Cache
    ) {}

    async execute(
        platformKey: PlatformKey,
        recentOnly: boolean,
        fullCycleInSeconds: number,
        delayBetweenCallsInSeconds: number
    ): Promise<void> {
        const currentStateStatusKey = `currentState.reviews.fetched.${platformKey}.status`;
        const currentStateLastTriedKey = `currentState.reviews.fetched.${platformKey}.lastTried`;
        const approximateCountCacheStoreTime = TimeInSeconds.HOUR;
        const approximateCountCacheStoreKey = `approximateReviewPlatformCount-${platformKey}`;

        const platformMatchAndLookupStages = [
            {
                $lookup: {
                    from: 'platforms',
                    localField: '_id',
                    foreignField: 'restaurantId',
                    as: 'platforms',
                },
            },
            {
                $unwind: {
                    path: '$platforms',
                    preserveNullAndEmptyArrays: false,
                },
            },
            {
                $match: {
                    'platforms.key': platformKey,
                },
            },
        ];

        const successMatchStage = {
            $match: {
                active: true,
                [currentStateStatusKey]: PlatformDataFetchedStatus.SUCCESS,
            },
        };

        const errorMatchStage = {
            $match: {
                active: true,
                [currentStateStatusKey]: PlatformDataFetchedStatus.ERROR,
                [currentStateLastTriedKey]: { $lt: DateTime.now().minus({ days: 1 }).toJSDate() }, // Retry after 24h
            },
        };

        let approximateReviewPlatformCount = await this._cache.get(approximateCountCacheStoreKey);
        if (approximateReviewPlatformCount === null) {
            const { count } = (
                await this._restaurantsRepository.aggregate([successMatchStage, ...platformMatchAndLookupStages, { $count: 'count' }])
            )[0] ?? { count: 0 };
            approximateReviewPlatformCount = count;
            await this._cache.set(approximateCountCacheStoreKey, approximateReviewPlatformCount, approximateCountCacheStoreTime);
        }

        const parallelPlatformCountToRefresh = Math.ceil(
            approximateReviewPlatformCount / (fullCycleInSeconds / delayBetweenCallsInSeconds)
        );

        const staleSuccessRestaurants =
            approximateReviewPlatformCount > 0
                ? await this._restaurantsRepository.aggregate([
                      successMatchStage,
                      ...platformMatchAndLookupStages,
                      {
                          $project: {
                              _id: 1,
                              name: 1,
                              socialId: 1,
                          },
                      },
                      {
                          $sort: {
                              [currentStateLastTriedKey]: 1,
                          },
                      },
                      {
                          $limit: parallelPlatformCountToRefresh,
                      },
                  ])
                : [];

        const staleErrorRestaurants = await this._restaurantsRepository.aggregate([
            errorMatchStage,
            ...platformMatchAndLookupStages,
            {
                $project: {
                    _id: 1,
                    name: 1,
                    socialId: 1,
                },
            },
        ]);

        logger.info('[RefreshStalePlatformReviewsUseCase] - refreshing stale restaurants', {
            platformKey,
            approximateReviewPlatformCount,
            staleSuccessRestaurantCount: staleSuccessRestaurants.length,
            staleSuccessRestaurants: staleSuccessRestaurants.map((r) => ({ id: r._id.toString(), name: r.name, socialId: r.socialId })),
            staleErrorRestaurantCount: staleErrorRestaurants.length,
            staleErrorRestaurants: staleErrorRestaurants.map((r) => ({ id: r._id.toString(), name: r.name, socialId: r.socialId })),
        });

        const platformsToRefresh = await this._platformsRepository.find({
            filter: {
                key: platformKey,
                restaurantId: { $in: [...staleSuccessRestaurants, ...staleErrorRestaurants].map((r) => r._id) },
            },
            projection: { _id: 1, socialId: 1 },
            options: { lean: true },
        });

        await Promise.all(platformsToRefresh.map(({ _id }) => sendFetchPlatformReviewsMessage(_id, recentOnly)));
    }
}
