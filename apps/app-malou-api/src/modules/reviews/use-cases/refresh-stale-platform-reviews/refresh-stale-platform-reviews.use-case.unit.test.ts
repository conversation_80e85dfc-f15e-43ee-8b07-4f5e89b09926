import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import { PlatformDataFetchedStatus, PlatformKey, TimeInSeconds } from '@malou-io/package-utils';

import { InjectionToken } from ':helpers/injection';
import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultPlatform } from ':modules/platforms/tests/platform.builder';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import * as updateReviewsProducer from ':modules/reviews/queues/update-reviews/update-reviews.producer';
import { RefreshStalePlatformReviewsUseCase } from ':modules/reviews/use-cases/refresh-stale-platform-reviews/refresh-stale-platform-reviews.use-case';
import { Cache } from ':plugins/cache';

// Mock the producer
jest.mock(':modules/reviews/queues/update-reviews/update-reviews.producer');
const mockSendFetchPlatformReviewsMessage = updateReviewsProducer.send as jest.MockedFunction<typeof updateReviewsProducer.send>;

let refreshStalePlatformReviewsUseCase: RefreshStalePlatformReviewsUseCase;
let restaurantsRepository: RestaurantsRepository;
let mockCache: jest.Mocked<Cache>;

describe('RefreshStalePlatformReviewsUseCase', () => {
    beforeEach(() => {
        container.clearInstances();
        registerRepositories(['RestaurantsRepository', 'PlatformsRepository']);

        // Create a mock cache
        mockCache = {
            get: jest.fn(),
            set: jest.fn(),
            delete: jest.fn(),
            status: jest.fn(),
            getKeys: jest.fn(),
            getTtl: jest.fn(),
        };

        // Register the mock cache
        container.register<Cache>(InjectionToken.Cache, { useValue: mockCache });

        refreshStalePlatformReviewsUseCase = container.resolve(RefreshStalePlatformReviewsUseCase);
        restaurantsRepository = container.resolve(RestaurantsRepository);

        // Clear all mocks
        jest.clearAllMocks();
    });

    describe('execute', () => {
        const platformKey = PlatformKey.GMB;
        const recentOnly = true;
        const fullCycleInSeconds = 6 * TimeInSeconds.HOUR;
        const delayBetweenCallsInSeconds = TimeInSeconds.MINUTE;

        it.only('should handle comprehensive scenario with all possible states', async () => {
            // Setup test data with different restaurant states
            const now = DateTime.now();
            const oneDayAgo = now.minus({ days: 1 });
            const twoDaysAgo = now.minus({ days: 2 });
            const oneHourAgo = now.minus({ hours: 1 });

            // Restaurant IDs
            const successRestaurant1Id = newDbId();
            const successRestaurant2Id = newDbId();
            const successRestaurant3Id = newDbId();
            const errorRestaurant1Id = newDbId(); // Error > 24h ago (should retry)
            const errorRestaurant2Id = newDbId(); // Error < 24h ago (should not retry)
            const inactiveRestaurantId = newDbId(); // Inactive restaurant
            const noPlatformRestaurantId = newDbId(); // No platform linked

            // Platform IDs
            const successPlatform1Id = newDbId();
            const successPlatform2Id = newDbId();
            const successPlatform3Id = newDbId();
            const errorPlatform1Id = newDbId();
            const errorPlatform2Id = newDbId();

            const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                // Success restaurants with different lastTried dates
                                getDefaultRestaurant()
                                    ._id(successRestaurant1Id)
                                    .name('Success Restaurant 1')
                                    .socialId('success-social-1')
                                    .active(true)
                                    .build(),
                                getDefaultRestaurant()
                                    ._id(successRestaurant2Id)
                                    .name('Success Restaurant 2')
                                    .socialId('success-social-2')
                                    .active(true)
                                    .build(),
                                getDefaultRestaurant()
                                    ._id(successRestaurant3Id)
                                    .name('Success Restaurant 3')
                                    .socialId('success-social-3')
                                    .active(true)
                                    .build(),
                                // Error restaurants
                                getDefaultRestaurant()
                                    ._id(errorRestaurant1Id)
                                    .name('Error Restaurant 1')
                                    .socialId('error-social-1')
                                    .active(true)
                                    .build(),
                                getDefaultRestaurant()
                                    ._id(errorRestaurant2Id)
                                    .name('Error Restaurant 2')
                                    .socialId('error-social-2')
                                    .active(true)
                                    .build(),
                                // Inactive restaurant (should be ignored)
                                getDefaultRestaurant()
                                    ._id(inactiveRestaurantId)
                                    .name('Inactive Restaurant')
                                    .socialId('inactive-social')
                                    .active(false)
                                    .build(),
                                // Restaurant without platform (should be ignored)
                                getDefaultRestaurant()
                                    ._id(noPlatformRestaurantId)
                                    .name('No Platform Restaurant')
                                    .socialId('no-platform-social')
                                    .active(true)
                                    .build(),
                            ];
                        },
                    },
                    platforms: {
                        data() {
                            return [
                                // Success platforms
                                getDefaultPlatform()
                                    ._id(successPlatform1Id)
                                    .restaurantId(successRestaurant1Id)
                                    .key(platformKey)
                                    .socialId('success-platform-1')
                                    .build(),
                                getDefaultPlatform()
                                    ._id(successPlatform2Id)
                                    .restaurantId(successRestaurant2Id)
                                    .key(platformKey)
                                    .socialId('success-platform-2')
                                    .build(),
                                getDefaultPlatform()
                                    ._id(successPlatform3Id)
                                    .restaurantId(successRestaurant3Id)
                                    .key(platformKey)
                                    .socialId('success-platform-3')
                                    .build(),
                                // Error platforms
                                getDefaultPlatform()
                                    ._id(errorPlatform1Id)
                                    .restaurantId(errorRestaurant1Id)
                                    .key(platformKey)
                                    .socialId('error-platform-1')
                                    .build(),
                                getDefaultPlatform()
                                    ._id(errorPlatform2Id)
                                    .restaurantId(errorRestaurant2Id)
                                    .key(platformKey)
                                    .socialId('error-platform-2')
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            // Set up restaurant states using direct database updates
            const currentStateStatusKey = `currentState.reviews.fetched.${platformKey}.status`;
            const currentStateLastTriedKey = `currentState.reviews.fetched.${platformKey}.lastTried`;

            // Update success restaurants with different lastTried dates
            await restaurantsRepository.findOneAndUpdate({
                filter: { _id: successRestaurant1Id },
                update: {
                    [currentStateStatusKey]: PlatformDataFetchedStatus.SUCCESS,
                    [currentStateLastTriedKey]: twoDaysAgo.toJSDate(), // Oldest, should be first
                },
            });

            await restaurantsRepository.findOneAndUpdate({
                filter: { _id: successRestaurant2Id },
                update: {
                    [currentStateStatusKey]: PlatformDataFetchedStatus.SUCCESS,
                    [currentStateLastTriedKey]: oneDayAgo.toJSDate(), // Second oldest
                },
            });

            await restaurantsRepository.findOneAndUpdate({
                filter: { _id: successRestaurant3Id },
                update: {
                    [currentStateStatusKey]: PlatformDataFetchedStatus.SUCCESS,
                    [currentStateLastTriedKey]: oneHourAgo.toJSDate(), // Most recent
                },
            });

            // Update error restaurants
            await restaurantsRepository.findOneAndUpdate({
                filter: { _id: errorRestaurant1Id },
                update: {
                    [currentStateStatusKey]: PlatformDataFetchedStatus.ERROR,
                    [currentStateLastTriedKey]: twoDaysAgo.toJSDate(), // > 24h ago, should retry
                },
            });

            await restaurantsRepository.findOneAndUpdate({
                filter: { _id: errorRestaurant2Id },
                update: {
                    [currentStateStatusKey]: PlatformDataFetchedStatus.ERROR,
                    [currentStateLastTriedKey]: oneHourAgo.toJSDate(), // < 24h ago, should not retry
                },
            });

            // Mock cache to return null initially (no cached count)
            mockCache.get.mockResolvedValue(null);
            mockCache.set.mockResolvedValue();

            // Mock the producer
            mockSendFetchPlatformReviewsMessage.mockResolvedValue(undefined);

            // Execute the use case
            const result = await refreshStalePlatformReviewsUseCase.execute(
                platformKey,
                recentOnly,
                fullCycleInSeconds,
                delayBetweenCallsInSeconds
            );

            // Verify cache interactions
            expect(mockCache.get).toHaveBeenCalledWith(`approximateReviewPlatformCount-${platformKey}`);
            expect(mockCache.set).toHaveBeenCalledWith(
                `approximateReviewPlatformCount-${platformKey}`,
                3, // 3 success restaurants
                TimeInSeconds.HOUR
            );

            // Calculate expected parallel count
            const expectedParallelCount = Math.ceil(3 / (fullCycleInSeconds / delayBetweenCallsInSeconds));

            // Verify producer calls
            // Should call for:
            // - Success restaurants (limited by parallelPlatformCountToRefresh)
            // - Error restaurant 1 (> 24h ago)
            // Should NOT call for:
            // - Error restaurant 2 (< 24h ago)
            // - Inactive restaurant
            // - Restaurant without platform
            expect(mockSendFetchPlatformReviewsMessage).toHaveBeenCalledTimes(expectedParallelCount + 1); // success + 1 error

            // Verify the calls were made with correct platform IDs
            const calledPlatformIds = mockSendFetchPlatformReviewsMessage.mock.calls.map((call) => call[0]);
            expect(calledPlatformIds).toContain(errorPlatform1Id); // Error > 24h should be included
            expect(calledPlatformIds).not.toContain(errorPlatform2Id); // Error < 24h should not be included

            // Verify all calls used the correct recentOnly parameter
            mockSendFetchPlatformReviewsMessage.mock.calls.forEach((call) => {
                expect(call[1]).toBe(recentOnly);
            });

            // Verify return value structure
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBe(expectedParallelCount + 1); // success + 1 error
            result.forEach((item) => {
                expect(item).toHaveProperty('socialId');
                expect(typeof item.socialId).toBe('string');
            });

            // Verify specific social IDs in result
            const resultSocialIds = result.map((r) => r.socialId);
            expect(resultSocialIds).toContain('error-platform-1'); // Error > 24h should be included
            expect(resultSocialIds).not.toContain('error-platform-2'); // Error < 24h should not be included
        });

        it('should use cached count when available', async () => {
            const cachedCount = 10;
            const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [];
                        },
                    },
                    platforms: {
                        data() {
                            return [];
                        },
                    },
                },
            });

            await testCase.build();

            // Mock cache to return cached value
            mockCache.get.mockResolvedValue(cachedCount);
            mockCache.set.mockResolvedValue();
            mockSendFetchPlatformReviewsMessage.mockResolvedValue(undefined);

            await refreshStalePlatformReviewsUseCase.execute(platformKey, recentOnly, fullCycleInSeconds, delayBetweenCallsInSeconds);

            // Verify cache was checked but not set (since value was cached)
            expect(mockCache.get).toHaveBeenCalledWith(`approximateReviewPlatformCount-${platformKey}`);
            expect(mockCache.set).not.toHaveBeenCalled();
        });

        it('should handle empty results gracefully', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [];
                        },
                    },
                    platforms: {
                        data() {
                            return [];
                        },
                    },
                },
            });

            await testCase.build();

            mockCache.get.mockResolvedValue(null);
            mockCache.set.mockResolvedValue();
            mockSendFetchPlatformReviewsMessage.mockResolvedValue(undefined);

            const result = await refreshStalePlatformReviewsUseCase.execute(
                platformKey,
                recentOnly,
                fullCycleInSeconds,
                delayBetweenCallsInSeconds
            );

            expect(result).toEqual([]);
            expect(mockSendFetchPlatformReviewsMessage).not.toHaveBeenCalled();
            expect(mockCache.set).toHaveBeenCalledWith(`approximateReviewPlatformCount-${platformKey}`, 0, TimeInSeconds.HOUR);
        });

        it('should only process restaurants with matching platform key', async () => {
            const restaurantWithGmbId = newDbId();
            const restaurantWithFacebookId = newDbId();
            const gmbPlatformId = newDbId();
            const facebookPlatformId = newDbId();

            const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant()
                                    ._id(restaurantWithGmbId)
                                    .name('GMB Restaurant')
                                    .socialId('gmb-social')
                                    .active(true)
                                    .build(),
                                getDefaultRestaurant()
                                    ._id(restaurantWithFacebookId)
                                    .name('Facebook Restaurant')
                                    .socialId('facebook-social')
                                    .active(true)
                                    .build(),
                            ];
                        },
                    },
                    platforms: {
                        data() {
                            return [
                                getDefaultPlatform()
                                    ._id(gmbPlatformId)
                                    .restaurantId(restaurantWithGmbId)
                                    .key(PlatformKey.GMB)
                                    .socialId('gmb-platform')
                                    .build(),
                                getDefaultPlatform()
                                    ._id(facebookPlatformId)
                                    .restaurantId(restaurantWithFacebookId)
                                    .key(PlatformKey.FACEBOOK)
                                    .socialId('facebook-platform')
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            // Set both restaurants to SUCCESS status
            const currentStateStatusKey = `currentState.reviews.fetched.${PlatformKey.GMB}.status`;
            const currentStateLastTriedKey = `currentState.reviews.fetched.${PlatformKey.GMB}.lastTried`;
            const facebookStatusKey = `currentState.reviews.fetched.${PlatformKey.FACEBOOK}.status`;
            const facebookLastTriedKey = `currentState.reviews.fetched.${PlatformKey.FACEBOOK}.lastTried`;

            await restaurantsRepository.findOneAndUpdate({
                filter: { _id: restaurantWithGmbId },
                update: {
                    [currentStateStatusKey]: PlatformDataFetchedStatus.SUCCESS,
                    [currentStateLastTriedKey]: DateTime.now().minus({ days: 2 }).toJSDate(),
                },
            });

            await restaurantsRepository.findOneAndUpdate({
                filter: { _id: restaurantWithFacebookId },
                update: {
                    [facebookStatusKey]: PlatformDataFetchedStatus.SUCCESS,
                    [facebookLastTriedKey]: DateTime.now().minus({ days: 2 }).toJSDate(),
                },
            });

            mockCache.get.mockResolvedValue(null);
            mockCache.set.mockResolvedValue();
            mockSendFetchPlatformReviewsMessage.mockResolvedValue(undefined);

            // Execute for GMB platform key
            const result = await refreshStalePlatformReviewsUseCase.execute(
                PlatformKey.GMB,
                recentOnly,
                fullCycleInSeconds,
                delayBetweenCallsInSeconds
            );

            // Should only process GMB restaurant, not Facebook
            expect(result).toHaveLength(1);
            expect(result[0].socialId).toBe('gmb-platform');
            expect(mockSendFetchPlatformReviewsMessage).toHaveBeenCalledTimes(1);
            expect(mockSendFetchPlatformReviewsMessage).toHaveBeenCalledWith(gmbPlatformId, recentOnly);
        });

        it('should respect parallel platform count limit for success restaurants', async () => {
            // Create more success restaurants than the parallel limit
            const restaurantIds = Array.from({ length: 10 }, () => newDbId());
            const platformIds = Array.from({ length: 10 }, () => newDbId());

            const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms'>({
                seeds: {
                    restaurants: {
                        data() {
                            return restaurantIds.map((id, index) =>
                                getDefaultRestaurant()
                                    ._id(id)
                                    .name(`Success Restaurant ${index}`)
                                    .socialId(`success-social-${index}`)
                                    .active(true)
                                    .build()
                            );
                        },
                    },
                    platforms: {
                        data() {
                            return platformIds.map((id, index) =>
                                getDefaultPlatform()
                                    ._id(id)
                                    .restaurantId(restaurantIds[index])
                                    .key(platformKey)
                                    .socialId(`success-platform-${index}`)
                                    .build()
                            );
                        },
                    },
                },
            });

            await testCase.build();

            // Set all restaurants to SUCCESS status with different lastTried dates
            const currentStateStatusKey = `currentState.reviews.fetched.${platformKey}.status`;
            const currentStateLastTriedKey = `currentState.reviews.fetched.${platformKey}.lastTried`;

            for (let i = 0; i < restaurantIds.length; i++) {
                await restaurantsRepository.findOneAndUpdate({
                    filter: { _id: restaurantIds[i] },
                    update: {
                        [currentStateStatusKey]: PlatformDataFetchedStatus.SUCCESS,
                        [currentStateLastTriedKey]: DateTime.now()
                            .minus({ days: i + 1 })
                            .toJSDate(),
                    },
                });
            }

            mockCache.get.mockResolvedValue(null);
            mockCache.set.mockResolvedValue();
            mockSendFetchPlatformReviewsMessage.mockResolvedValue(undefined);

            const result = await refreshStalePlatformReviewsUseCase.execute(
                platformKey,
                recentOnly,
                fullCycleInSeconds,
                delayBetweenCallsInSeconds
            );

            // Calculate expected parallel count
            const expectedParallelCount = Math.ceil(10 / (fullCycleInSeconds / delayBetweenCallsInSeconds));

            // Should respect the parallel limit
            expect(result).toHaveLength(expectedParallelCount);
            expect(mockSendFetchPlatformReviewsMessage).toHaveBeenCalledTimes(expectedParallelCount);

            // Verify cache was set with total count
            expect(mockCache.set).toHaveBeenCalledWith(`approximateReviewPlatformCount-${platformKey}`, 10, TimeInSeconds.HOUR);
        });

        it('should handle different platform keys correctly', async () => {
            const restaurantId = newDbId();
            const platformId = newDbId();

            const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant()
                                    ._id(restaurantId)
                                    .name('Test Restaurant')
                                    .socialId('test-social')
                                    .active(true)
                                    .build(),
                            ];
                        },
                    },
                    platforms: {
                        data() {
                            return [
                                getDefaultPlatform()
                                    ._id(platformId)
                                    .restaurantId(restaurantId)
                                    .key(PlatformKey.TRIPADVISOR)
                                    .socialId('tripadvisor-platform')
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            // Set restaurant to SUCCESS status for TripAdvisor
            const currentStateStatusKey = `currentState.reviews.fetched.${PlatformKey.TRIPADVISOR}.status`;
            const currentStateLastTriedKey = `currentState.reviews.fetched.${PlatformKey.TRIPADVISOR}.lastTried`;

            await restaurantsRepository.findOneAndUpdate({
                filter: { _id: restaurantId },
                update: {
                    [currentStateStatusKey]: PlatformDataFetchedStatus.SUCCESS,
                    [currentStateLastTriedKey]: DateTime.now().minus({ days: 2 }).toJSDate(),
                },
            });

            mockCache.get.mockResolvedValue(null);
            mockCache.set.mockResolvedValue();
            mockSendFetchPlatformReviewsMessage.mockResolvedValue(undefined);

            const result = await refreshStalePlatformReviewsUseCase.execute(
                PlatformKey.TRIPADVISOR,
                recentOnly,
                fullCycleInSeconds,
                delayBetweenCallsInSeconds
            );

            expect(result).toHaveLength(1);
            expect(result[0].socialId).toBe('tripadvisor-platform');
            expect(mockCache.get).toHaveBeenCalledWith(`approximateReviewPlatformCount-${PlatformKey.TRIPADVISOR}`);
            expect(mockCache.set).toHaveBeenCalledWith(`approximateReviewPlatformCount-${PlatformKey.TRIPADVISOR}`, 1, TimeInSeconds.HOUR);
        });
    });
});
