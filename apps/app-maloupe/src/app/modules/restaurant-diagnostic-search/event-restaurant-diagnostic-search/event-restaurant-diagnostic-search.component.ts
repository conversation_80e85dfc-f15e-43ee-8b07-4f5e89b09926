import { NgTemplateOutlet } from '@angular/common';
import { Component, computed, inject, input, signal, WritableSignal } from '@angular/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MAT_AUTOCOMPLETE_DEFAULT_OPTIONS, MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { TranslateModule } from '@ngx-translate/core';

import { Locale, MaloupeEventName, MaloupeLocale, PersonType } from '@malou-io/package-utils';

import { HubSpotHttpService } from ':core/http-services/hubspot.http-service';
import { CookieManager } from ':core/storage/cookie-manager';
import { RestaurantDiagnosticSearchAbstractComponent } from ':modules/restaurant-diagnostic-search/restaurant-diagnostic-search-abstract.component';
import { DiagnosticHeaderComponent } from ':shared/components/diagnostic-header/diagnostic-header.component';
import { InputGoogleMapsAutocompleteComponent } from ':shared/components/input-google-maps-autocomplete/input-google-maps-autocomplete.component';
import { InputInstagramSearchComponent } from ':shared/components/input-instagram-search/input-instagram-search.component';
import { ToastDuration } from ':shared/components/toast/toast-item/toast-item.component';
import { PRIVACY_POLICY_EN_URL, PRIVACY_POLICY_FR_URL } from ':shared/constants';
import { CookieKey, RequestLocalStorageKey } from ':shared/enums/storage-key.enum';
import { Illustration } from ':shared/pipes';
import { IllustrationPathResolverPipe } from ':shared/pipes/illustration-path-resolver.pipe';
import { increaseRequestCount } from ':shared/utils';
import { getUserIdentityFromLocalStorage, isMalouUserIdentityInLocalStorage } from ':shared/utils/manage-user-uuid';
import { customEmailValidator, customPhoneNumberValidator } from ':shared/validators';

interface InformationForm {
    firstName: FormControl<string | null>;
    lastName: FormControl<string | null>;
    email: FormControl<string | null>;
    phoneNumber: FormControl<string | null>;
    locationCount: FormControl<number | null>;
    termsAndConditions: FormControl<boolean | null>;
}

@Component({
    selector: 'app-event-restaurant-diagnostic-search',
    templateUrl: './event-restaurant-diagnostic-search.component.html',
    styleUrl: './event-restaurant-diagnostic-search.component.scss',
    imports: [
        DiagnosticHeaderComponent,
        InputGoogleMapsAutocompleteComponent,
        InputInstagramSearchComponent,
        FormsModule,
        MatAutocompleteModule,
        MatButtonModule,
        MatFormFieldModule,
        MatIconModule,
        MatInputModule,
        MatSelectModule,
        MatSlideToggleModule,
        ReactiveFormsModule,
        TranslateModule,
        NgTemplateOutlet,
        IllustrationPathResolverPipe,
        MatCheckboxModule,
    ],
    providers: [{ provide: MAT_AUTOCOMPLETE_DEFAULT_OPTIONS, useValue: { overlayPanelClass: 'custom-option-panel' } }],
})
export class EventRestaurantDiagnosticSearchComponent extends RestaurantDiagnosticSearchAbstractComponent {
    readonly eventName = input.required<MaloupeEventName>();
    private readonly _hubSpotHttpService = inject(HubSpotHttpService);

    readonly Illustration = Illustration;

    readonly informationFormGroup: FormGroup<InformationForm> = this._formBuilder.group({
        firstName: [null as string | null, [Validators.required, Validators.minLength(2)]],
        lastName: [null as string | null, [Validators.required, Validators.minLength(2)]],
        email: [null as string | null, [Validators.required, customEmailValidator()]],
        phoneNumber: [null as string | null, [Validators.required, Validators.minLength(7), customPhoneNumberValidator()]],
        locationCount: [null as number | null, [Validators.required, Validators.min(1)]],
        termsAndConditions: [false as boolean | null, [Validators.requiredTrue]],
    });

    readonly isLoading: WritableSignal<boolean> = signal(false);

    get firstName(): string | null | undefined {
        return this.informationFormGroup.get('firstName')?.value;
    }

    get lastName(): string | null | undefined {
        return this.informationFormGroup.get('lastName')?.value;
    }

    get phoneNumber(): string | null | undefined {
        return this.informationFormGroup.get('phoneNumber')?.value;
    }

    get email(): string | null | undefined {
        return this.informationFormGroup.get('email')?.value;
    }

    get locationCount(): number | null | undefined {
        return this.informationFormGroup.get('locationCount')?.value;
    }

    get fistNameControl(): FormControl<string | null> {
        return this.informationFormGroup.get('firstName') as FormControl<string | null>;
    }

    get lastNameControl(): FormControl<string | null> {
        return this.informationFormGroup.get('lastName') as FormControl<string | null>;
    }

    get phoneNumberControl(): FormControl<string | null> {
        return this.informationFormGroup.get('phoneNumber') as FormControl<string | null>;
    }

    get emailControl(): FormControl<string | null> {
        return this.informationFormGroup.get('email') as FormControl<string | null>;
    }

    get locationCountControl(): FormControl<number | null> {
        return this.informationFormGroup.get('locationCount') as FormControl<number | null>;
    }

    get termsAndConditionsControl(): FormControl<boolean | null> {
        return this.informationFormGroup.get('termsAndConditions') as FormControl<boolean | null>;
    }

    readonly phonePlaceHolder = computed(() => {
        switch (this.eventName()) {
            case MaloupeEventName.NRA_2025:
                return '+1 xxx xxx xxxx';
            case MaloupeEventName.FSTEC_2025:
                return '****** xxx xxxx';
            default:
                return '07 89 98 65 43';
        }
    });

    computeMyDiagnostic(): void {
        const diagnostic = this.diagnostic();
        if (!diagnostic || this._isGetPlaceDiagnosticLimitExceeded()) {
            return;
        }
        increaseRequestCount({ key: RequestLocalStorageKey.GET_PLACE_DIAGNOSTIC_REQUEST });
        if (this.informationFormGroup.valid && diagnostic) {
            if (isMalouUserIdentityInLocalStorage()) {
                this._router.navigate(['compute-diagnostics', JSON.stringify([diagnostic.id])], {
                    queryParams: {
                        utm_source: this._utmSource,
                        utm_medium: this._utmMedium,
                        utm_campaign: this._utmCampaign,
                        event_name: this.eventName(),
                    },
                });
            } else {
                this._submitForm(diagnostic.id);
            }
        }
    }

    preventNonNumericalValue(event: KeyboardEvent): void {
        if (!event.key.match(/\s|[0-9]/)) {
            event.preventDefault();
        }
    }

    preventInvalidPhoneNumberInput(event: KeyboardEvent): void {
        const userInput = event.target as HTMLInputElement;
        const currentValue = userInput.value;

        if (this._isInvalidPhoneNumberKey(event.key, currentValue)) {
            event.preventDefault();
        }
    }

    redirectToPrivacyPolicy(): void {
        const lang = this._translateService.currentLang;
        switch (lang) {
            case Locale.EN:
                window.open(PRIVACY_POLICY_EN_URL, '_blank');
                break;
            default:
            case Locale.FR:
                window.open(PRIVACY_POLICY_FR_URL, '_blank');
                break;
        }
    }

    private _isInvalidPhoneNumberKey(key: string, currentValue: string): boolean {
        const isPlusSignAtStart = key === '+' && currentValue.length > 0;
        const isNotAllowedCharacter = !key.match(/[0-9\s]/) && key !== '+';
        return isPlusSignAtStart || isNotAllowedCharacter;
    }

    private _submitForm(malouDiagnosticId: string): void {
        const firstName = this.firstName;
        const lastName = this.lastName;
        const email = this.email;
        const phoneNumber = this.phoneNumber;
        const locationCount = this.locationCount;
        if (!firstName || !lastName || !email || !phoneNumber || !locationCount) {
            this._toastService.openErrorToast(
                this._translateService.instant('maloupe.diagnostic_search.event.user_info.error_fill'),
                ToastDuration.MEDIUM
            );
            return;
        }
        this.isLoading.set(true);
        const payload = {
            firstName: this.firstName,
            lastName: this.lastName,
            email: this.email,
            companyName: this.diagnostic()?.restaurant.name,
            phoneNumber: this.phoneNumber,
            locationCount: this.locationCount.toString(),
            zipCode: this.diagnostic()?.restaurant.address.postalCode ?? '',
            personType: PersonType.OTHER,
            utm_source: this._utmSource,
            utm_medium: this._utmMedium,
            utm_campaign: this._utmCampaign,
            utm_content: 'AppMaloupe',
            malouDiagnosticIds: [malouDiagnosticId],
            language: this._translateService.currentLang as MaloupeLocale,
            identity: getUserIdentityFromLocalStorage(),
            pageUri: window.location.href,
            hubspotutk: CookieManager.getCookie(CookieKey.HUBSPOT_UTK) ?? undefined,
        };
        this._hubSpotHttpService.submitForm(payload).subscribe({
            next: () => {
                this.isLoading.set(false);
                this._router.navigate(['compute-diagnostics', JSON.stringify([malouDiagnosticId])], {
                    queryParams: {
                        utm_source: this._utmSource,
                        utm_medium: this._utmMedium,
                        utm_campaign: this._utmCampaign,
                        event_name: this.eventName(),
                        first_name: firstName,
                        last_name: lastName,
                        email: email,
                        phone_number: phoneNumber,
                        location_count: locationCount,
                    },
                });
            },
            error: () => {
                this._toastService.openErrorToast(
                    this._translateService.instant('maloupe.diagnostic_search.event.error_submit'),
                    ToastDuration.MEDIUM
                );
                this.isLoading.set(false);
            },
        });
    }
}
