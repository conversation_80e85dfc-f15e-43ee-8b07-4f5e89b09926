---
import { Picture } from 'astro:assets';

// To type Astro component props, we have to name the interface "Props" literally
interface Props {
    stores: {
        name: string;
        relativePath: string;
    }[];
}

const links = [
    {
        title: 'Accueil',
        url: 'https://gardenicecafe.com/',
        dataSrc:
            'https://mediab.izipass.cloud/GardenIceCafeRefonte/Content/images/accueil-general/accueil-garden-ice-cafe.png',
    },
    {
        title: 'La carte',
        url: 'https://gardenicecafe.com/la-carte',
        dataSrc:
            'https://mediab.izipass.cloud/GardenIceCafeRefonte/Content/images/accueil-general/carte-garden-ice-cafe.png',
    },
    {
        title: 'Nos Restaurants',
        url: 'https://gardenicecafe.com/nos-restaurants',
        dataSrc: null,
    },
    {
        title: 'Club Privilège',
        url: 'https://gardenicecafe.com/carte-club-privilege',
        dataSrc:
            'https://mediab.izipass.cloud/GardenIceCafeRefonte/Content/images/accueil-general/privilege-garden-ice-cafe.png',
    },
    {
        title: 'Contact',
        url: 'https://gardenicecafe.com/contact',
        dataSrc:
            'https://mediab.izipass.cloud/GardenIceCafeRefonte/Content/images/accueil-general/contact-garden-ice-cafe.png',
    },
    {
        title: 'Emploi',
        url: 'https://gardenicecafe.com/emploi',
        dataSrc:
            'https://mediab.izipass.cloud/GardenIceCafeRefonte/Content/images/accueil-general/emploi-garden-ice-cafe.png',
    },
    {
        title: 'Franchise',
        url: 'https://gardenicecafe.com/devenez-franchise',
        dataSrc:
            'https://mediab.izipass.cloud/GardenIceCafeRefonte/Content/images/accueil-general/franchise-garden-ice-cafe.png',
    },
];
---

<style>
    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .dynamic-item {
        animation: slideDown 0.3s ease-out;
    }
</style>
<header
    class="bg-primary font-secondary px-xl-4 fixed top-0 left-0 z-100 w-full px-5 py-6"
>
    <div class="relative flex h-full items-start">
        <div class="flex w-1/3 justify-start">
            <button id="mobile-menu-button" aria-label="Toggle Menu">
                <svg
                    class="stroke-fourth h-9 w-9"
                    xmlns="http://www.w3.org/2000/svg"
                    ><g>
                        <path
                            d="M4 6H24M4 15H24M4 24H12"
                            stroke-width="2"
                            stroke-linecap="round"
                            stroke-linejoin="round"></path>
                    </g></svg
                >
            </button>
        </div>
        <div class="flex w-1/3 items-center justify-center">
            <div
                class="flex max-h-max max-w-max items-center justify-center sm:right-0"
            >
                <a
                    href="https://gardenicecafe.com/"
                    aria-label="Home Page"
                    class="block"
                >
                    <Picture
                        src="https://malou-production.s3.eu-west-3.amazonaws.com/store-locator/organization/6536a39cb6b3ebbdec97fced/favicons/logo.webp"
                        formats={['png']}
                        fallbackFormat="png"
                        alt="Logo Garden Ice Cafe"
                        inferSize
                        class="h-[50px] w-[150px]"
                        densities={[1, 2, 3]}
                    />
                </a>
            </div>
        </div>
        <div class="flex w-1/3 justify-end">
            <div
                class="border-secondary hover:bg-secondary text-fourth mr-4 flex min-w-fit items-center justify-center rounded-full border bg-none px-7 py-3 hover:text-black"
            >
                <a
                    class="font-secondary text-[16px] font-normal text-inherit uppercase"
                    >Commander en ligne</a
                >
            </div>
        </div>
    </div>

    <div
        id="mobile-menu"
        class="fixed top-0 right-0 z-40 flex h-full w-full -translate-y-full transform flex-col overflow-auto bg-[#1b1511] shadow-lg transition-transform duration-600 ease-in-out"
    >
        <div class="relative flex w-full">
            <div class="bg-primary flex w-1/2 justify-start">
                <button
                    id="close-menu"
                    aria-label="Close menu"
                    class="mt-8 ml-5 self-start"
                >
                    <svg
                        viewBox="0 0 16 16"
                        xmlns="http://www.w3.org/2000/svg"
                        class="fill-fourth h-6 w-6"
                    >
                        <path
                            fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M13.5303 2.46967C13.8232 2.76256 13.8232 3.23744 13.5303 3.53033L3.53033 13.5303C3.23744 13.8232 2.76256 13.8232 2.46967 13.5303C2.17678 13.2374 2.17678 12.7626 2.46967 12.4697L12.4697 2.46967C12.7626 2.17678 13.2374 2.17678 13.5303 2.46967Z"
                        ></path>
                        <path
                            fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M13.5303 13.5303C13.2374 13.8232 12.7626 13.8232 12.4697 13.5303L2.46967 3.53033C2.17678 3.23744 2.17678 2.76256 2.46967 2.46967C2.76256 2.17678 3.23744 2.17678 3.53033 2.46967L13.5303 12.4697C13.8232 12.7626 13.8232 13.2374 13.5303 13.5303Z"
                        ></path>
                    </svg>
                </button>
            </div>
            <div
                class="pointer-events-none absolute mt-6 flex w-full justify-center"
            >
                <div
                    class="flex max-h-max max-w-max items-center justify-center sm:right-0"
                >
                    <a
                        href="https://gardenicecafe.com/"
                        aria-label="Home Page"
                        class="block"
                    >
                        <Picture
                            src="https://malou-production.s3.eu-west-3.amazonaws.com/store-locator/organization/6536a39cb6b3ebbdec97fced/favicons/logo.webp"
                            formats={['png']}
                            fallbackFormat="png"
                            alt="Logo Garden Ice Cafe"
                            inferSize
                            class="h-[50px] w-[150px]"
                            densities={[1, 2, 3]}
                        />
                    </a>
                </div>
            </div>
            <div class="flex w-1/2 justify-end">
                <div
                    class="border-secondary hover:bg-secondary text-fourth mt-6.5 mr-8 flex min-w-fit items-center justify-center rounded-full border bg-none px-7 py-3 hover:text-black"
                >
                    <a
                        class="font-secondary text-[16px] font-normal text-inherit uppercase"
                        >Commander en ligne</a
                    >
                </div>
            </div>
        </div>

        <div class="flex h-full w-full">
            <div
                class="bg-primary flex h-full w-1/2 flex-col items-center justify-center"
            >
                <div class="p-6 pt-0">
                    <div class="flex flex-col items-start gap-10">
                        {
                            links.map((link, index) => (
                                <a
                                    href={link.url}
                                    data-item={index}
                                    data-src={link.dataSrc}
                                    class="item text-fourth hover:text-secondary font-secondary text-[26px] font-bold uppercase"
                                >
                                    {link.title}
                                </a>
                            ))
                        }
                    </div>
                </div>
            </div>
            <div
                class="relative flex w-1/2 flex-col px-2 pt-3"
                id="dynamic-content"
            >
                <div class="flex w-full items-center justify-center px-2">
                    {
                        links.map(
                            (link, index) =>
                                link.dataSrc && (
                                    <Picture
                                        id={`dynamic-item-${index}`}
                                        src={link.dataSrc!}
                                        formats={['png']}
                                        fallbackFormat="png"
                                        alt="image"
                                        inferSize
                                        class="dynamic-item hidden w-[450px]"
                                        densities={[1, 2, 3]}
                                    />
                                ),
                        )
                    }
                </div>
                <div id="restaurants-list" class="mt-36 hidden">
                    <div class="flex flex-col items-center gap-5">
                        {
                            [
                                'Brive-la-Gaillarde',
                                'Charleville-Mézières',
                                'Cognac',
                                'Montauban',
                                'Orléans',
                                'Périgueux',
                            ].map((city) => (
                                <div class="font-tertiary text-fourth text-[27px] font-normal italic">
                                    {city}
                                </div>
                            ))
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>

<script>
    const menu = document.getElementById('mobile-menu');
    const openBtn = document.getElementById('mobile-menu-button');
    const closeBtn = document.getElementById('close-menu');

    const items = document.querySelectorAll('.item');
    const dynamicItems = document.querySelectorAll('.dynamic-item');
    const restaurantsList = document.getElementById('restaurants-list');

    function openMenu() {
        menu?.classList.remove('-translate-y-full');
    }

    function closeMenu() {
        menu?.classList.add('-translate-y-full');
    }

    openBtn?.addEventListener('click', (e) => {
        e.stopPropagation();
        openMenu();
    });

    closeBtn?.addEventListener('click', (e) => {
        e.stopPropagation();
        closeMenu();
    });

    items.forEach((item) => {
        const el = item as HTMLElement;
        el.addEventListener('mouseenter', () => {
            if (el.dataset.src) {
                dynamicItems.forEach((di) => di.classList.add('hidden'));
                restaurantsList?.classList.add('hidden');
                const index = el.dataset.item;
                const dynamicItem = document.getElementById(
                    `dynamic-item-${index}`,
                );
                dynamicItem?.classList.remove('hidden');
            } else if (!el.dataset.type) {
                dynamicItems.forEach((di) => di.classList.add('hidden'));
                restaurantsList?.classList.toggle('hidden');
            }
        });
    });
</script>
