import { config } from ':config';

export enum ComponentType {
    HEADER = 'header',
    FOOTER = 'footer',
    MAP_LIST = 'mapList',
}

const componentMap: Record<
    string,
    Record<ComponentType, () => Promise<any>>
> = {
    'groupe-rollroll-x-bolkiri': {
        [ComponentType.HEADER]: () =>
            import(':components/custom/bolkiri/Header.astro'),
        [ComponentType.FOOTER]: () =>
            import(':components/custom/bolkiri/Footer.astro'),
        [ComponentType.MAP_LIST]: () => Promise.resolve({ default: null }),
    },
    'krispy-kreme': {
        [ComponentType.HEADER]: () =>
            import(':components/custom/krispy-kreme/Header.astro'),
        [ComponentType.FOOTER]: () =>
            import(':components/custom/krispy-kreme/Footer.astro'),
        [ComponentType.MAP_LIST]: () =>
            import(':components/custom/krispy-kreme/MapList.astro'),
    },
    bioburger: {
        [ComponentType.HEADER]: () =>
            import(':components/custom/bioburger/Header.astro'),
        [ComponentType.FOOTER]: () =>
            import(':components/custom/bioburger/Footer.astro'),
        [ComponentType.MAP_LIST]: () => Promise.resolve({ default: null }),
    },
    'breads-bakery': {
        [ComponentType.HEADER]: () =>
            import(':components/custom/breads-bakery/Header.astro'),
        [ComponentType.FOOTER]: () =>
            import(':components/custom/breads-bakery/Footer.astro'),
        [ComponentType.MAP_LIST]: () => Promise.resolve({ default: null }),
    },
    'garden-ice-cafe': {
        [ComponentType.HEADER]: () =>
            import(':components/custom/garden-ice-cafe/Header.astro'),
        [ComponentType.FOOTER]: () =>
            import(':components/custom/garden-ice-cafe/Footer.astro'),
        [ComponentType.MAP_LIST]: () => Promise.resolve({ default: null }),
    },
};

// Function to dynamically load a component (header, footer, css, etc.)
export async function loadComponent({
    organizationName,
    component,
}: {
    organizationName: string;
    component: ComponentType;
}): Promise<any> {
    const organizationKey = organizationName.toLowerCase().replaceAll(' ', '-');
    const defaultOrganizationKey = 'bioburger'; // For non production environments

    const importComponent = !['production', 'test'].includes(config.environment)
        ? componentMap[defaultOrganizationKey]?.[component]
        : componentMap[organizationKey]?.[component];

    if (!importComponent) {
        throw new Error(
            `Component "${component}" not found for organization "${organizationName}".`,
        );
    }

    return (await importComponent()).default;
}
